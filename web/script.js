// Global variables
let serverUrl = '';
let apiKey = '';
let isConnected = false;

// Utility functions
function log(message, type = 'info') {
    const logElement = document.getElementById('activityLog');
    const timestamp = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;
    entry.textContent = `[${timestamp}] ${message}`;
    logElement.appendChild(entry);
    logElement.scrollTop = logElement.scrollHeight;
}

function clearLog() {
    document.getElementById('activityLog').innerHTML = '';
}

function updateConnectionStatus(connected) {
    isConnected = connected;
    const statusElement = document.getElementById('connectionStatus');
    const authSection = document.getElementById('authSection');
    const controlPanel = document.getElementById('controlPanel');
    
    if (connected) {
        statusElement.textContent = 'Connected';
        statusElement.className = 'status connected';
        authSection.classList.add('hidden');
        controlPanel.classList.remove('hidden');
        log('Connected to VSCode Remote Control server', 'success');
    } else {
        statusElement.textContent = 'Disconnected';
        statusElement.className = 'status disconnected';
        authSection.classList.remove('hidden');
        controlPanel.classList.add('hidden');
        log('Disconnected from server', 'error');
    }
}

// API request function
async function apiRequest(endpoint, method = 'GET', data = null) {
    if (!isConnected && endpoint !== '/health') {
        log('Not connected to server', 'error');
        return null;
    }

    const url = `${serverUrl}${endpoint}`;
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        }
    };

    if (apiKey) {
        options.headers['X-API-Key'] = apiKey;
    }

    if (data) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(url, options);
        const result = await response.json();
        
        if (!response.ok) {
            log(`API Error: ${result.error || 'Unknown error'}`, 'error');
            return null;
        }

        return result;
    } catch (error) {
        log(`Network Error: ${error.message}`, 'error');
        return null;
    }
}

// Connection functions
async function connect() {
    serverUrl = document.getElementById('serverUrl').value.trim();
    apiKey = document.getElementById('apiKey').value.trim();
    
    if (!serverUrl) {
        log('Please enter a server URL', 'error');
        return;
    }

    log('Attempting to connect...');
    
    const result = await apiRequest('/health');
    if (result && result.success) {
        updateConnectionStatus(true);
        await getServerStatus();
    } else {
        updateConnectionStatus(false);
    }
}

// File operations
async function openFile() {
    const filePath = document.getElementById('filePath').value.trim();
    if (!filePath) {
        log('Please enter a file path', 'error');
        return;
    }

    const result = await apiRequest('/api/files/open', 'POST', { path: filePath });
    if (result && result.success) {
        log(`Opened file: ${result.data.path}`, 'success');
        await getEditorInfo();
    }
}

async function listFiles() {
    const path = document.getElementById('filePath').value.trim() || undefined;
    const result = await apiRequest(`/api/files${path ? `?path=${encodeURIComponent(path)}` : ''}`);
    
    if (result && result.success) {
        const fileList = document.getElementById('fileList');
        fileList.innerHTML = '';
        
        result.data.forEach(file => {
            const item = document.createElement('div');
            item.className = `file-item ${file.isDirectory ? 'directory' : ''}`;
            item.textContent = `${file.isDirectory ? '📁' : '📄'} ${file.name}`;
            item.onclick = () => {
                if (file.isDirectory) {
                    document.getElementById('filePath').value = file.path;
                    listFiles();
                } else {
                    document.getElementById('filePath').value = file.path;
                }
            };
            fileList.appendChild(item);
        });
        
        log(`Listed ${result.data.length} items`, 'success');
    }
}

async function saveFile() {
    const result = await apiRequest('/api/files/save', 'POST');
    if (result && result.success) {
        log(`Saved file: ${result.data.path}`, 'success');
    }
}

// Editor operations
async function getEditorInfo() {
    const result = await apiRequest('/api/editor');
    if (result && result.success) {
        const info = result.data;
        const editorInfo = document.getElementById('editorInfo');
        editorInfo.innerHTML = `
            <strong>File:</strong> ${info.document.fileName}<br>
            <strong>Language:</strong> ${info.document.languageId}<br>
            <strong>Lines:</strong> ${info.document.lineCount}<br>
            <strong>Dirty:</strong> ${info.document.isDirty ? 'Yes' : 'No'}<br>
            <strong>Selection:</strong> Line ${info.selection.start.line}, Char ${info.selection.start.character} - Line ${info.selection.end.line}, Char ${info.selection.end.character}
        `;
        log('Retrieved editor information', 'success');
    }
}

async function moveCursor() {
    const line = parseInt(document.getElementById('cursorLine').value);
    const character = parseInt(document.getElementById('cursorChar').value);
    
    if (isNaN(line) || isNaN(character)) {
        log('Please enter valid line and character numbers', 'error');
        return;
    }

    const result = await apiRequest('/api/editor/cursor', 'POST', { line, character });
    if (result && result.success) {
        log(`Moved cursor to line ${line}, character ${character}`, 'success');
    }
}

async function insertText() {
    const text = document.getElementById('editText').value;
    if (!text) {
        log('Please enter text to insert', 'error');
        return;
    }

    const result = await apiRequest('/api/editor/edit', 'POST', { text });
    if (result && result.success) {
        log('Text inserted successfully', 'success');
        document.getElementById('editText').value = '';
    }
}

async function formatDocument() {
    const result = await apiRequest('/api/editor/format', 'POST');
    if (result && result.success) {
        log('Document formatted successfully', 'success');
    }
}

// Workspace operations
async function getWorkspaceInfo() {
    const result = await apiRequest('/api/workspace');
    if (result && result.success) {
        const info = result.data;
        log(`Workspace: ${info.name || 'Unnamed'}, Folders: ${info.folders.length}`, 'success');
    }
}

async function openFolder() {
    const folderPath = document.getElementById('folderPath').value.trim();
    if (!folderPath) {
        log('Please enter a folder path', 'error');
        return;
    }

    const result = await apiRequest('/api/workspace/open', 'POST', { path: folderPath });
    if (result && result.success) {
        log(`Opened folder: ${folderPath}`, 'success');
    }
}

async function getOpenEditors() {
    const result = await apiRequest('/api/workspace/editors');
    if (result && result.success) {
        log(`Found ${result.data.length} open editors`, 'success');
    }
}

// Debug operations
async function getDebugSessions() {
    const result = await apiRequest('/api/debug/sessions');
    if (result && result.success) {
        log(`Active debug sessions: ${result.data.sessions.length}`, 'success');
    }
}

async function startDebugging() {
    const result = await apiRequest('/api/debug/start', 'POST');
    if (result && result.success) {
        log('Debugging started', 'success');
    }
}

async function stopDebugging() {
    const result = await apiRequest('/api/debug/stop', 'POST');
    if (result && result.success) {
        log('Debugging stopped', 'success');
    }
}

async function debugAction(action) {
    const result = await apiRequest('/api/debug/action', 'POST', { action });
    if (result && result.success) {
        log(`Debug action executed: ${action}`, 'success');
    }
}

// Terminal operations
async function getTerminals() {
    const result = await apiRequest('/api/terminal');
    if (result && result.success) {
        log(`Found ${result.data.length} terminals`, 'success');
    }
}

async function createTerminal() {
    const result = await apiRequest('/api/terminal/create', 'POST', { name: 'Remote Terminal' });
    if (result && result.success) {
        log(`Created terminal: ${result.data.name}`, 'success');
    }
}

async function sendTerminalCommand() {
    const command = document.getElementById('terminalCommand').value.trim();
    if (!command) {
        log('Please enter a command', 'error');
        return;
    }

    const result = await apiRequest('/api/terminal/send', 'POST', { text: command });
    if (result && result.success) {
        log(`Sent command: ${command}`, 'success');
        document.getElementById('terminalCommand').value = '';
    }
}

// Server status
async function getServerStatus() {
    const result = await apiRequest('/status');
    if (result && result.success) {
        const info = result.data;
        const serverInfo = document.getElementById('serverInfo');
        serverInfo.innerHTML = `
            <strong>Status:</strong> ${info.running ? 'Running' : 'Stopped'}<br>
            <strong>Port:</strong> ${info.port || 'N/A'}<br>
            <strong>Uptime:</strong> ${info.startTime ? Math.floor((Date.now() - info.startTime) / 1000) + 's' : 'N/A'}
        `;
        log('Retrieved server status', 'success');
    }
}

async function healthCheck() {
    const result = await apiRequest('/health');
    if (result && result.success) {
        log(`Health check passed. Uptime: ${Math.floor(result.data.uptime / 1000)}s`, 'success');
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    log('VSCode Remote Control Web Panel loaded');
});
