<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VSCode Remote Control</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1e1e1e;
            color: #cccccc;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #252526;
            border-radius: 8px;
        }

        .header h1 {
            color: #007acc;
            margin-bottom: 10px;
        }

        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }

        .status.connected {
            background-color: #4caf50;
            color: white;
        }

        .status.disconnected {
            background-color: #f44336;
            color: white;
        }

        .auth-section {
            background-color: #252526;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .panel {
            background-color: #252526;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #3c3c3c;
        }

        .panel h3 {
            color: #007acc;
            margin-bottom: 15px;
            border-bottom: 1px solid #3c3c3c;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #cccccc;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            background-color: #3c3c3c;
            border: 1px solid #555;
            border-radius: 4px;
            color: #cccccc;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007acc;
        }

        .btn {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #005a9e;
        }

        .btn.secondary {
            background-color: #6c757d;
        }

        .btn.secondary:hover {
            background-color: #545b62;
        }

        .btn.danger {
            background-color: #dc3545;
        }

        .btn.danger:hover {
            background-color: #c82333;
        }

        .file-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #3c3c3c;
            border-radius: 4px;
            padding: 10px;
            background-color: #1e1e1e;
        }

        .file-item {
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 2px;
        }

        .file-item:hover {
            background-color: #3c3c3c;
        }

        .file-item.directory {
            color: #007acc;
            font-weight: bold;
        }

        .editor-info {
            background-color: #1e1e1e;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #3c3c3c;
            margin-bottom: 15px;
        }

        .log {
            background-color: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-entry.error {
            color: #f44336;
        }

        .log-entry.success {
            color: #4caf50;
        }

        .log-entry.info {
            color: #2196f3;
        }

        .hidden {
            display: none;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .position-input {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>VSCode Remote Control</h1>
            <div class="status" id="connectionStatus">Disconnected</div>
        </div>

        <div class="auth-section" id="authSection">
            <h3>Authentication</h3>
            <div class="form-group">
                <label for="serverUrl">Server URL:</label>
                <input type="text" id="serverUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
            </div>
            <div class="form-group">
                <label for="apiKey">API Key (optional):</label>
                <input type="password" id="apiKey" placeholder="Enter API key if required">
            </div>
            <button class="btn" onclick="connect()">Connect</button>
        </div>

        <div class="control-panel hidden" id="controlPanel">
            <!-- File Operations -->
            <div class="panel">
                <h3>File Operations</h3>
                <div class="form-group">
                    <label for="filePath">File Path:</label>
                    <input type="text" id="filePath" placeholder="/path/to/file">
                </div>
                <button class="btn" onclick="openFile()">Open File</button>
                <button class="btn secondary" onclick="listFiles()">List Files</button>
                <button class="btn secondary" onclick="saveFile()">Save Current File</button>
                
                <div class="file-list" id="fileList"></div>
            </div>

            <!-- Editor Operations -->
            <div class="panel">
                <h3>Editor Operations</h3>
                <button class="btn" onclick="getEditorInfo()">Get Editor Info</button>
                <button class="btn secondary" onclick="formatDocument()">Format Document</button>
                
                <div class="editor-info" id="editorInfo"></div>
                
                <div class="form-group">
                    <label>Cursor Position:</label>
                    <div class="position-input">
                        <input type="number" id="cursorLine" placeholder="Line" min="0">
                        <input type="number" id="cursorChar" placeholder="Character" min="0">
                    </div>
                </div>
                <button class="btn" onclick="moveCursor()">Move Cursor</button>
                
                <div class="form-group">
                    <label for="editText">Text to Insert:</label>
                    <textarea id="editText" rows="3" placeholder="Enter text to insert"></textarea>
                </div>
                <button class="btn" onclick="insertText()">Insert Text</button>
            </div>

            <!-- Workspace Operations -->
            <div class="panel">
                <h3>Workspace Operations</h3>
                <button class="btn" onclick="getWorkspaceInfo()">Get Workspace Info</button>
                <button class="btn secondary" onclick="getOpenEditors()">Get Open Editors</button>
                
                <div class="form-group">
                    <label for="folderPath">Folder Path:</label>
                    <input type="text" id="folderPath" placeholder="/path/to/folder">
                </div>
                <button class="btn" onclick="openFolder()">Open Folder</button>
            </div>

            <!-- Debug Operations -->
            <div class="panel">
                <h3>Debug Operations</h3>
                <button class="btn" onclick="getDebugSessions()">Get Debug Sessions</button>
                <button class="btn" onclick="startDebugging()">Start Debugging</button>
                <button class="btn danger" onclick="stopDebugging()">Stop Debugging</button>
                
                <div class="grid-2">
                    <button class="btn secondary" onclick="debugAction('continue')">Continue</button>
                    <button class="btn secondary" onclick="debugAction('stepOver')">Step Over</button>
                    <button class="btn secondary" onclick="debugAction('stepInto')">Step Into</button>
                    <button class="btn secondary" onclick="debugAction('stepOut')">Step Out</button>
                </div>
            </div>

            <!-- Terminal Operations -->
            <div class="panel">
                <h3>Terminal Operations</h3>
                <button class="btn" onclick="getTerminals()">Get Terminals</button>
                <button class="btn" onclick="createTerminal()">Create Terminal</button>
                
                <div class="form-group">
                    <label for="terminalCommand">Command:</label>
                    <input type="text" id="terminalCommand" placeholder="Enter command">
                </div>
                <button class="btn" onclick="sendTerminalCommand()">Send Command</button>
            </div>

            <!-- Server Status -->
            <div class="panel">
                <h3>Server Status</h3>
                <button class="btn" onclick="getServerStatus()">Get Status</button>
                <button class="btn secondary" onclick="healthCheck()">Health Check</button>
                
                <div class="editor-info" id="serverInfo"></div>
            </div>
        </div>

        <div class="panel">
            <h3>Activity Log</h3>
            <button class="btn secondary" onclick="clearLog()">Clear Log</button>
            <div class="log" id="activityLog"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
