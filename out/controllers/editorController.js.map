{"version": 3, "file": "editorController.js", "sourceRoot": "", "sources": ["../../src/controllers/editorController.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAIjC,MAAa,gBAAgB;IAElB,KAAK,CAAC,aAAa,CAAC,GAAoB,EAAE,GAAqB;QAClE,IAAI;YACA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACrF;YAED,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;YACvC,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;YACzC,MAAM,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;YAEjD,MAAM,UAAU,GAAe;gBAC3B,QAAQ,EAAE;oBACN,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAClC;gBACD,SAAS,EAAE;oBACP,KAAK,EAAE;wBACH,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI;wBAC1B,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS;qBACvC;oBACD,GAAG,EAAE;wBACD,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI;wBACxB,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS;qBACrC;oBACD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;iBACpC;gBACD,YAAY,EAAE;oBACV,KAAK,EAAE;wBACH,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;wBACvC,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,IAAI,CAAC;qBACpD;oBACD,GAAG,EAAE;wBACD,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC;wBACrC,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS,IAAI,CAAC;qBAClD;iBACJ;aACJ,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;SACnD;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,GAAoB,EAAE,GAAqB;QAC7D,IAAI;YACA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAoB,GAAG,CAAC,IAAI,CAAC;YAClD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACrF;YAED,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAClD,IAAI,KAAK,EAAE;oBACP,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,CAChC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,EAC5D,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAC3D,CAAC;oBACF,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;iBAC1C;qBAAM;oBACH,uBAAuB;oBACvB,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBAC3D;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,OAAO,EAAE;gBACT,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;aACzD;iBAAM;gBACH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC,CAAC;aACjF;SACJ;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,GAAoB,EAAE,GAAqB;QAC/D,IAAI;YACA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAsB,GAAG,CAAC,IAAI,CAAC;YAChE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACrF;YAED,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAEtD,IAAI,MAAM,EAAE;gBACR,WAAW;gBACX,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACnF,YAAY,CAAC,SAAS,GAAG,YAAY,CAAC;aACzC;iBAAM;gBACH,WAAW;gBACX,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC9D,YAAY,CAAC,SAAS,GAAG,YAAY,CAAC;aACzC;YAED,UAAU;YACV,YAAY,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;YAE/D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;gBAC/B,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC7B,QAAQ,EAAE,MAAM,IAAI,KAAK;aAC5B,CAAC,CAAC,CAAC;SACP;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,GAAoB,EAAE,GAAqB;QAC/D,IAAI;YACA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAyB,GAAG,CAAC,IAAI,CAAC;YACtD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACrF;YAED,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAEnE,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC;YACnC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAEpC,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE9D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;gBAC/B,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;gBACzB,IAAI,EAAE,YAAY;aACrB,CAAC,CAAC,CAAC;SACP;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,GAAoB,EAAE,GAAqB;QACnE,IAAI;YACA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACrF;YAED,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;YAErE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;SAC5D;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,GAAoB,EAAE,GAAqB;QACpE,IAAI;YACA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACrF;YAED,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;YACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEhC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;gBAC/B,IAAI;gBACJ,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC9B,CAAC,CAAC,CAAC;SACP;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;IAEO,cAAc,CAAI,OAAgB,EAAE,IAAQ,EAAE,KAAc;QAChE,OAAO;YACH,OAAO;YACP,IAAI;YACJ,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;CACJ;AA5LD,4CA4LC"}