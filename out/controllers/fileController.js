"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileController = void 0;
const vscode = require("vscode");
const fs = require("fs");
const path = require("path");
class FileController {
    async listFiles(req, res) {
        try {
            const dirPath = req.query.path || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
            if (!dirPath) {
                return res.status(400).json(this.createResponse(false, null, 'No workspace folder found'));
            }
            const files = await this.getDirectoryContents(dirPath);
            res.json(this.createResponse(true, files));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async getFile(req, res) {
        try {
            const filePath = req.params[0]; // 获取通配符匹配的路径
            if (!filePath) {
                return res.status(400).json(this.createResponse(false, null, 'File path is required'));
            }
            const fullPath = path.isAbsolute(filePath) ? filePath :
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);
            const content = await fs.promises.readFile(fullPath, 'utf8');
            const stats = await fs.promises.stat(fullPath);
            const fileInfo = {
                path: fullPath,
                name: path.basename(fullPath),
                isDirectory: stats.isDirectory(),
                size: stats.size,
                lastModified: stats.mtime.getTime(),
                content
            };
            res.json(this.createResponse(true, fileInfo));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async openFile(req, res) {
        try {
            const { path: filePath, viewColumn, preserveFocus } = req.body;
            if (!filePath) {
                return res.status(400).json(this.createResponse(false, null, 'File path is required'));
            }
            const fullPath = path.isAbsolute(filePath) ? filePath :
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);
            const uri = vscode.Uri.file(fullPath);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document, {
                viewColumn: viewColumn,
                preserveFocus: preserveFocus || false
            });
            res.json(this.createResponse(true, {
                path: document.fileName,
                lineCount: document.lineCount,
                languageId: document.languageId
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async saveFile(req, res) {
        try {
            const { path: filePath, content } = req.body;
            if (filePath && content !== undefined) {
                // 保存指定路径的文件
                const fullPath = path.isAbsolute(filePath) ? filePath :
                    path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);
                await fs.promises.writeFile(fullPath, content, 'utf8');
                res.json(this.createResponse(true, { path: fullPath, saved: true }));
            }
            else {
                // 保存当前活动文件
                const activeEditor = vscode.window.activeTextEditor;
                if (!activeEditor) {
                    return res.status(400).json(this.createResponse(false, null, 'No active editor'));
                }
                if (content !== undefined) {
                    // 替换当前文档内容
                    const document = activeEditor.document;
                    const fullRange = new vscode.Range(document.positionAt(0), document.positionAt(document.getText().length));
                    await activeEditor.edit(editBuilder => {
                        editBuilder.replace(fullRange, content);
                    });
                }
                await activeEditor.document.save();
                res.json(this.createResponse(true, {
                    path: activeEditor.document.fileName,
                    saved: true
                }));
            }
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async deleteFile(req, res) {
        try {
            const filePath = req.params[0];
            if (!filePath) {
                return res.status(400).json(this.createResponse(false, null, 'File path is required'));
            }
            const fullPath = path.isAbsolute(filePath) ? filePath :
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);
            await fs.promises.unlink(fullPath);
            res.json(this.createResponse(true, { path: fullPath, deleted: true }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async getDirectoryContents(dirPath) {
        const items = await fs.promises.readdir(dirPath);
        const files = [];
        for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stats = await fs.promises.stat(itemPath);
            files.push({
                path: itemPath,
                name: item,
                isDirectory: stats.isDirectory(),
                size: stats.isDirectory() ? undefined : stats.size,
                lastModified: stats.mtime.getTime()
            });
        }
        return files.sort((a, b) => {
            // 目录排在前面
            if (a.isDirectory && !b.isDirectory)
                return -1;
            if (!a.isDirectory && b.isDirectory)
                return 1;
            return a.name.localeCompare(b.name);
        });
    }
    createResponse(success, data, error) {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
exports.FileController = FileController;
//# sourceMappingURL=fileController.js.map