"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalController = void 0;
const vscode = require("vscode");
class TerminalController {
    constructor() {
        this.terminals = new Map();
    }
    async getTerminals(req, res) {
        try {
            const terminals = vscode.window.terminals;
            const terminalInfo = await Promise.all(terminals.map(async (terminal) => ({
                name: terminal.name,
                processId: await terminal.processId,
                creationOptions: terminal.creationOptions
            })));
            res.json(this.createResponse(true, terminalInfo));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async createTerminal(req, res) {
        try {
            const { name, cwd, env } = req.body;
            const options = {
                name: name || 'Remote Terminal',
                cwd: cwd,
                env: env
            };
            const terminal = vscode.window.createTerminal(options);
            // 存储终端引用
            this.terminals.set(terminal.name, terminal);
            // 显示终端
            terminal.show();
            res.json(this.createResponse(true, {
                name: terminal.name,
                processId: await terminal.processId,
                created: true
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async sendText(req, res) {
        try {
            const { text, addNewLine } = req.body;
            const { terminalName } = req.query;
            if (!text) {
                return res.status(400).json(this.createResponse(false, null, 'Text is required'));
            }
            let terminal;
            if (terminalName) {
                // 发送到指定终端
                terminal = vscode.window.terminals.find(t => t.name === terminalName);
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
                }
            }
            else {
                // 发送到活动终端
                terminal = vscode.window.activeTerminal;
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'No active terminal'));
                }
            }
            terminal.sendText(text, addNewLine !== false);
            res.json(this.createResponse(true, {
                terminalName: terminal.name,
                textSent: text,
                addNewLine: addNewLine !== false
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async showTerminal(req, res) {
        try {
            const { terminalName } = req.body;
            let terminal;
            if (terminalName) {
                terminal = vscode.window.terminals.find(t => t.name === terminalName);
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
                }
            }
            else {
                terminal = vscode.window.activeTerminal;
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'No active terminal'));
                }
            }
            terminal.show();
            res.json(this.createResponse(true, {
                terminalName: terminal.name,
                shown: true
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async hideTerminal(req, res) {
        try {
            const { terminalName } = req.body;
            let terminal;
            if (terminalName) {
                terminal = vscode.window.terminals.find(t => t.name === terminalName);
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
                }
            }
            else {
                terminal = vscode.window.activeTerminal;
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'No active terminal'));
                }
            }
            terminal.hide();
            res.json(this.createResponse(true, {
                terminalName: terminal.name,
                hidden: true
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async disposeTerminal(req, res) {
        try {
            const { terminalName } = req.body;
            if (!terminalName) {
                return res.status(400).json(this.createResponse(false, null, 'Terminal name is required'));
            }
            const terminal = vscode.window.terminals.find(t => t.name === terminalName);
            if (!terminal) {
                return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
            }
            terminal.dispose();
            this.terminals.delete(terminalName);
            res.json(this.createResponse(true, {
                terminalName,
                disposed: true
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    createResponse(success, data, error) {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
exports.TerminalController = TerminalController;
//# sourceMappingURL=terminalController.js.map