"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditorController = void 0;
const vscode = require("vscode");
class EditorController {
    async getEditorInfo(req, res) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }
            const document = activeEditor.document;
            const selection = activeEditor.selection;
            const visibleRanges = activeEditor.visibleRanges;
            const editorInfo = {
                document: {
                    fileName: document.fileName,
                    languageId: document.languageId,
                    lineCount: document.lineCount,
                    isDirty: document.isDirty,
                    isUntitled: document.isUntitled
                },
                selection: {
                    start: {
                        line: selection.start.line,
                        character: selection.start.character
                    },
                    end: {
                        line: selection.end.line,
                        character: selection.end.character
                    },
                    text: document.getText(selection)
                },
                visibleRange: {
                    start: {
                        line: visibleRanges[0]?.start.line || 0,
                        character: visibleRanges[0]?.start.character || 0
                    },
                    end: {
                        line: visibleRanges[0]?.end.line || 0,
                        character: visibleRanges[0]?.end.character || 0
                    }
                }
            };
            res.json(this.createResponse(true, editorInfo));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async editText(req, res) {
        try {
            const { range, text } = req.body;
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }
            const success = await activeEditor.edit(editBuilder => {
                if (range) {
                    const vscodeRange = new vscode.Range(new vscode.Position(range.start.line, range.start.character), new vscode.Position(range.end.line, range.end.character));
                    editBuilder.replace(vscodeRange, text);
                }
                else {
                    // 如果没有指定范围，在当前光标位置插入文本
                    editBuilder.insert(activeEditor.selection.active, text);
                }
            });
            if (success) {
                res.json(this.createResponse(true, { edited: true }));
            }
            else {
                res.status(500).json(this.createResponse(false, null, 'Failed to edit text'));
            }
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async moveCursor(req, res) {
        try {
            const { line, character, select } = req.body;
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }
            const position = new vscode.Position(line, character);
            if (select) {
                // 扩展选择到新位置
                const newSelection = new vscode.Selection(activeEditor.selection.anchor, position);
                activeEditor.selection = newSelection;
            }
            else {
                // 移动光标到新位置
                const newSelection = new vscode.Selection(position, position);
                activeEditor.selection = newSelection;
            }
            // 滚动到光标位置
            activeEditor.revealRange(new vscode.Range(position, position));
            res.json(this.createResponse(true, {
                position: { line, character },
                selected: select || false
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async selectText(req, res) {
        try {
            const { start, end } = req.body;
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }
            const startPosition = new vscode.Position(start.line, start.character);
            const endPosition = new vscode.Position(end.line, end.character);
            const selection = new vscode.Selection(startPosition, endPosition);
            activeEditor.selection = selection;
            activeEditor.revealRange(selection);
            const selectedText = activeEditor.document.getText(selection);
            res.json(this.createResponse(true, {
                selection: { start, end },
                text: selectedText
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async formatDocument(req, res) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }
            await vscode.commands.executeCommand('editor.action.formatDocument');
            res.json(this.createResponse(true, { formatted: true }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    async getDocumentText(req, res) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }
            const document = activeEditor.document;
            const text = document.getText();
            res.json(this.createResponse(true, {
                text,
                lineCount: document.lineCount,
                fileName: document.fileName
            }));
        }
        catch (error) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }
    createResponse(success, data, error) {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
exports.EditorController = EditorController;
//# sourceMappingURL=editorController.js.map