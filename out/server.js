"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoteControlServer = void 0;
const vscode = require("vscode");
const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const fileController_1 = require("./controllers/fileController");
const editorController_1 = require("./controllers/editorController");
const workspaceController_1 = require("./controllers/workspaceController");
const debugController_1 = require("./controllers/debugController");
const terminalController_1 = require("./controllers/terminalController");
class RemoteControlServer {
    constructor(context) {
        this.context = context;
        this.app = express();
        // 初始化控制器
        this.fileController = new fileController_1.FileController();
        this.editorController = new editorController_1.EditorController();
        this.workspaceController = new workspaceController_1.WorkspaceController();
        this.debugController = new debugController_1.DebugController();
        this.terminalController = new terminalController_1.TerminalController();
        this.setupMiddleware();
        this.setupRoutes();
    }
    setupMiddleware() {
        // CORS配置
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const allowedOrigins = config.get('allowedOrigins') || ['*'];
        this.app.use(cors({
            origin: allowedOrigins.includes('*') ? true : allowedOrigins,
            credentials: true
        }));
        // 解析JSON请求体
        this.app.use(bodyParser.json({ limit: '10mb' }));
        this.app.use(bodyParser.urlencoded({ extended: true }));
        // API密钥认证中间件
        this.app.use(this.authMiddleware.bind(this));
        // 错误处理中间件在路由之后添加
    }
    authMiddleware(req, res, next) {
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const apiKey = config.get('apiKey');
        // 如果没有设置API密钥，则跳过认证
        if (!apiKey) {
            return next();
        }
        const providedKey = req.headers['x-api-key'] || req.query.apiKey;
        if (providedKey !== apiKey) {
            return res.status(401).json(this.createResponse(false, null, 'Invalid API key'));
        }
        next();
    }
    errorHandler(error, req, res, next) {
        console.error('API Error:', error);
        res.status(500).json(this.createResponse(false, null, error.message || 'Internal server error'));
    }
    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json(this.createResponse(true, { status: 'ok', uptime: Date.now() - (this.startTime || 0) }));
        });
        // 服务器状态
        this.app.get('/status', (req, res) => {
            res.json(this.createResponse(true, this.getStatus()));
        });
        // 文件操作路由
        this.app.get('/api/files', (req, res) => this.fileController.listFiles(req, res));
        this.app.get('/api/files/*', (req, res) => this.fileController.getFile(req, res));
        this.app.post('/api/files/open', (req, res) => this.fileController.openFile(req, res));
        this.app.post('/api/files/save', (req, res) => this.fileController.saveFile(req, res));
        this.app.delete('/api/files/*', (req, res) => this.fileController.deleteFile(req, res));
        // 编辑器操作路由
        this.app.get('/api/editor', (req, res) => this.editorController.getEditorInfo(req, res));
        this.app.post('/api/editor/edit', (req, res) => this.editorController.editText(req, res));
        this.app.post('/api/editor/cursor', (req, res) => this.editorController.moveCursor(req, res));
        this.app.post('/api/editor/select', (req, res) => this.editorController.selectText(req, res));
        this.app.post('/api/editor/format', (req, res) => this.editorController.formatDocument(req, res));
        // 工作区操作路由
        this.app.get('/api/workspace', (req, res) => this.workspaceController.getWorkspaceInfo(req, res));
        this.app.post('/api/workspace/open', (req, res) => this.workspaceController.openFolder(req, res));
        // 调试操作路由
        this.app.get('/api/debug/sessions', (req, res) => this.debugController.getDebugSessions(req, res));
        this.app.post('/api/debug/start', (req, res) => this.debugController.startDebugging(req, res));
        this.app.post('/api/debug/stop', (req, res) => this.debugController.stopDebugging(req, res));
        this.app.post('/api/debug/breakpoint', (req, res) => this.debugController.toggleBreakpoint(req, res));
        // 终端操作路由
        this.app.get('/api/terminal', (req, res) => this.terminalController.getTerminals(req, res));
        this.app.post('/api/terminal/create', (req, res) => this.terminalController.createTerminal(req, res));
        this.app.post('/api/terminal/send', (req, res) => this.terminalController.sendText(req, res));
        // 静态文件服务 - Web控制面板
        this.app.use('/web', express.static(require('path').join(__dirname, '../web')));
        // 错误处理中间件
        this.app.use(this.errorHandler.bind(this));
    }
    start() {
        if (this.server) {
            vscode.window.showWarningMessage('Remote Control Server is already running');
            return;
        }
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const port = config.get('port') || 8080;
        this.server = this.app.listen(port, () => {
            this.startTime = Date.now();
            vscode.window.showInformationMessage(`Remote Control Server started on port ${port}`);
            console.log(`VSCode Remote Control Server listening on port ${port}`);
        });
        this.server?.on('error', (error) => {
            vscode.window.showErrorMessage(`Failed to start server: ${error.message}`);
            this.server = undefined;
        });
    }
    stop() {
        if (this.server) {
            this.server.close(() => {
                vscode.window.showInformationMessage('Remote Control Server stopped');
                console.log('VSCode Remote Control Server stopped');
            });
            this.server = undefined;
            this.startTime = undefined;
        }
        else {
            vscode.window.showWarningMessage('Remote Control Server is not running');
        }
    }
    getStatus() {
        return {
            running: !!this.server,
            port: this.server ? this.server.address()?.port : undefined,
            startTime: this.startTime
        };
    }
    createResponse(success, data, error) {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
exports.RemoteControlServer = RemoteControlServer;
//# sourceMappingURL=server.js.map