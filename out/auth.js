"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimiter = exports.requirePermission = exports.PERMISSIONS = exports.AuthManager = void 0;
const vscode = require("vscode");
const crypto = require("crypto");
class AuthManager {
    constructor() {
        this.apiKeys = new Set();
        this.sessionTokens = new Map();
        this.loadApiKeys();
    }
    static getInstance() {
        if (!AuthManager.instance) {
            AuthManager.instance = new AuthManager();
        }
        return AuthManager.instance;
    }
    loadApiKeys() {
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const apiKey = config.get('apiKey');
        if (apiKey) {
            this.apiKeys.add(apiKey);
        }
    }
    validateApiKey(key) {
        if (this.apiKeys.size === 0) {
            // 如果没有设置API密钥，则允许所有请求
            return true;
        }
        return this.apiKeys.has(key);
    }
    generateSessionToken(permissions = ['read', 'write']) {
        const token = crypto.randomBytes(32).toString('hex');
        const expires = Date.now() + (24 * 60 * 60 * 1000); // 24小时后过期
        this.sessionTokens.set(token, { expires, permissions });
        return token;
    }
    validateSessionToken(token) {
        const session = this.sessionTokens.get(token);
        if (!session) {
            return { valid: false };
        }
        if (Date.now() > session.expires) {
            this.sessionTokens.delete(token);
            return { valid: false };
        }
        return { valid: true, permissions: session.permissions };
    }
    revokeSessionToken(token) {
        return this.sessionTokens.delete(token);
    }
    cleanupExpiredTokens() {
        const now = Date.now();
        for (const [token, session] of this.sessionTokens.entries()) {
            if (now > session.expires) {
                this.sessionTokens.delete(token);
            }
        }
    }
    addApiKey(key) {
        this.apiKeys.add(key);
        this.saveApiKeys();
    }
    removeApiKey(key) {
        const removed = this.apiKeys.delete(key);
        if (removed) {
            this.saveApiKeys();
        }
        return removed;
    }
    listApiKeys() {
        return Array.from(this.apiKeys).map(key => key.substring(0, 8) + '...' + key.substring(key.length - 8));
    }
    saveApiKeys() {
        // 注意：在实际应用中，API密钥应该存储在安全的地方
        // 这里只是示例，实际使用时应该考虑加密存储
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const keys = Array.from(this.apiKeys);
        if (keys.length > 0) {
            config.update('apiKey', keys[0], vscode.ConfigurationTarget.Global);
        }
    }
    hasPermission(token, permission) {
        const validation = this.validateSessionToken(token);
        if (!validation.valid || !validation.permissions) {
            return false;
        }
        return validation.permissions.includes(permission) || validation.permissions.includes('admin');
    }
    generateApiKey() {
        return crypto.randomBytes(32).toString('hex');
    }
}
exports.AuthManager = AuthManager;
// 权限常量
exports.PERMISSIONS = {
    READ: 'read',
    WRITE: 'write',
    DEBUG: 'debug',
    TERMINAL: 'terminal',
    WORKSPACE: 'workspace',
    ADMIN: 'admin'
};
// 中间件函数
function requirePermission(permission) {
    return (req, res, next) => {
        const authManager = AuthManager.getInstance();
        const token = req.headers['x-session-token'];
        if (!token) {
            return res.status(401).json({
                success: false,
                error: 'Session token required',
                timestamp: Date.now()
            });
        }
        if (!authManager.hasPermission(token, permission)) {
            return res.status(403).json({
                success: false,
                error: `Permission '${permission}' required`,
                timestamp: Date.now()
            });
        }
        next();
    };
}
exports.requirePermission = requirePermission;
// 速率限制
class RateLimiter {
    constructor(maxRequests = 100, windowMs = 60000) {
        this.requests = new Map();
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
    }
    isAllowed(clientId) {
        const now = Date.now();
        const requests = this.requests.get(clientId) || [];
        // 清理过期的请求记录
        const validRequests = requests.filter(time => now - time < this.windowMs);
        if (validRequests.length >= this.maxRequests) {
            return false;
        }
        validRequests.push(now);
        this.requests.set(clientId, validRequests);
        return true;
    }
    getRemainingRequests(clientId) {
        const requests = this.requests.get(clientId) || [];
        const now = Date.now();
        const validRequests = requests.filter(time => now - time < this.windowMs);
        return Math.max(0, this.maxRequests - validRequests.length);
    }
}
exports.RateLimiter = RateLimiter;
//# sourceMappingURL=auth.js.map