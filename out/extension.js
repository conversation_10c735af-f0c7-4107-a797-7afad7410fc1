"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const server_1 = require("./server");
let server;
function activate(context) {
    console.log('VSCode Remote Control extension is now active!');
    // 创建服务器实例
    server = new server_1.RemoteControlServer(context);
    // 注册命令
    const startCommand = vscode.commands.registerCommand('vscode-remote-control.start', () => {
        server?.start();
    });
    const stopCommand = vscode.commands.registerCommand('vscode-remote-control.stop', () => {
        server?.stop();
    });
    const statusCommand = vscode.commands.registerCommand('vscode-remote-control.status', () => {
        const status = server?.getStatus();
        vscode.window.showInformationMessage(`Remote Control Server: ${status?.running ? 'Running on port ' + status.port : 'Stopped'}`);
    });
    context.subscriptions.push(startCommand, stopCommand, statusCommand);
    // 如果配置了自动启动，则启动服务器
    const config = vscode.workspace.getConfiguration('vscode-remote-control');
    if (config.get('autoStart')) {
        server.start();
    }
}
exports.activate = activate;
function deactivate() {
    server?.stop();
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map