{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../src/auth.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,iCAAiC;AAEjC,MAAa,WAAW;IAKpB;QAHQ,YAAO,GAAgB,IAAI,GAAG,EAAE,CAAC;QACjC,kBAAa,GAA4D,IAAI,GAAG,EAAE,CAAC;QAGvF,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAEM,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACvB,WAAW,CAAC,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;SAC5C;QACD,OAAO,WAAW,CAAC,QAAQ,CAAC;IAChC,CAAC;IAEO,WAAW;QACf,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAS,QAAQ,CAAC,CAAC;QAE5C,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAC5B;IACL,CAAC;IAEM,cAAc,CAAC,GAAW;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;YACzB,sBAAsB;YACtB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAEM,oBAAoB,CAAC,cAAwB,CAAC,MAAM,EAAE,OAAO,CAAC;QACjE,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU;QAE9D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QACxD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,oBAAoB,CAAC,KAAa;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;SAC3B;QAED,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE;YAC9B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;SAC3B;QAED,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC;IAC7D,CAAC;IAEM,kBAAkB,CAAC,KAAa;QACnC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAEM,oBAAoB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;YACzD,IAAI,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE;gBACvB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACpC;SACJ;IACL,CAAC;IAEM,SAAS,CAAC,GAAW;QACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAEM,YAAY,CAAC,GAAW;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,WAAW;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACtC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAC9D,CAAC;IACN,CAAC;IAEO,WAAW;QACf,4BAA4B;QAC5B,uBAAuB;QACvB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1E,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACvE;IACL,CAAC;IAEM,aAAa,CAAC,KAAa,EAAE,UAAkB;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACnG,CAAC;IAEM,cAAc;QACjB,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;CACJ;AA7GD,kCA6GC;AAED,OAAO;AACM,QAAA,WAAW,GAAG;IACvB,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,OAAO;CACR,CAAC;AAEX,QAAQ;AACR,SAAgB,iBAAiB,CAAC,UAAkB;IAChD,OAAO,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;QACrC,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAE7C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACxB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB;gBAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;SACN;QAED,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE;YAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACxB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,eAAe,UAAU,YAAY;gBAC5C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;SACN;QAED,IAAI,EAAE,CAAC;IACX,CAAC,CAAC;AACN,CAAC;AAvBD,8CAuBC;AAED,OAAO;AACP,MAAa,WAAW;IAKpB,YAAY,cAAsB,GAAG,EAAE,WAAmB,KAAK;QAJvD,aAAQ,GAA0B,IAAI,GAAG,EAAE,CAAC;QAKhD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAEM,SAAS,CAAC,QAAgB;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEnD,YAAY;QACZ,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1E,IAAI,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YAC1C,OAAO,KAAK,CAAC;SAChB;QAED,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,oBAAoB,CAAC,QAAgB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IAChE,CAAC;CACJ;AAhCD,kCAgCC"}