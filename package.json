{"name": "vscode-remote-control", "displayName": "VSCode Remote Control", "description": "Remote control VSCode through HTTP API", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "vscode-remote-control.start", "title": "Start Remote Control Server"}, {"command": "vscode-remote-control.stop", "title": "Stop Remote Control Server"}, {"command": "vscode-remote-control.status", "title": "Remote Control Status"}], "configuration": {"title": "VSCode Remote Control", "properties": {"vscode-remote-control.port": {"type": "number", "default": 8080, "description": "Port for the remote control server"}, "vscode-remote-control.apiKey": {"type": "string", "default": "", "description": "API key for authentication (leave empty to disable)"}, "vscode-remote-control.autoStart": {"type": "boolean", "default": false, "description": "Automatically start server when VSCode starts"}, "vscode-remote-control.allowedOrigins": {"type": "array", "items": {"type": "string"}, "default": ["*"], "description": "Allowed origins for CORS"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "16.x", "@types/vscode": "^1.74.0", "typescript": "^4.9.4"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2"}}