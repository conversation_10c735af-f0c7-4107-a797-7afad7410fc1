# VSCode Remote Control Extension

一个强大的VSCode插件，允许通过HTTP API远程控制VSCode的各种功能，包括文件操作、编辑器控制、调试管理等。

## 功能特性

- 🚀 **远程文件操作**: 打开、保存、删除文件，浏览文件系统
- ✏️ **编辑器控制**: 文本编辑、光标移动、文本选择、格式化
- 🗂️ **工作区管理**: 打开文件夹、管理编辑器标签页
- 🐛 **调试控制**: 启动/停止调试、设置断点、单步执行
- 💻 **终端操作**: 创建终端、发送命令
- 🔒 **安全认证**: API密钥认证、会话管理
- 🌐 **Web控制面板**: 图形化远程控制界面
- ⚡ **实时状态**: 获取编辑器和工作区状态信息

## 安装

1. 克隆或下载此项目
2. 在项目根目录运行：
   ```bash
   npm install
   npm run compile
   ```
3. 在VSCode中按 `F5` 启动调试，或者打包安装：
   ```bash
   vsce package
   code --install-extension vscode-remote-control-0.0.1.vsix
   ```

## 配置

在VSCode设置中配置以下选项：

- `vscode-remote-control.port`: 服务器端口 (默认: 8080)
- `vscode-remote-control.apiKey`: API密钥 (可选，留空则不需要认证)
- `vscode-remote-control.autoStart`: 自动启动服务器 (默认: false)
- `vscode-remote-control.allowedOrigins`: 允许的CORS源 (默认: ["*"])

## 使用方法

### 启动服务器

1. 通过命令面板 (`Ctrl+Shift+P`) 运行 "Start Remote Control Server"
2. 或者在设置中启用 `autoStart` 自动启动

### Web控制面板

访问 `http://localhost:8080/web` 打开图形化控制面板。

### API接口

#### 健康检查
```http
GET /health
```

#### 文件操作
```http
# 列出文件
GET /api/files?path=/path/to/directory

# 获取文件内容
GET /api/files/path/to/file

# 打开文件
POST /api/files/open
{
  "path": "/path/to/file",
  "viewColumn": 1,
  "preserveFocus": false
}

# 保存文件
POST /api/files/save
{
  "path": "/path/to/file",
  "content": "file content"
}
```

#### 编辑器操作
```http
# 获取编辑器信息
GET /api/editor

# 编辑文本
POST /api/editor/edit
{
  "range": {
    "start": {"line": 0, "character": 0},
    "end": {"line": 0, "character": 5}
  },
  "text": "new text"
}

# 移动光标
POST /api/editor/cursor
{
  "line": 10,
  "character": 5,
  "select": false
}

# 选择文本
POST /api/editor/select
{
  "start": {"line": 0, "character": 0},
  "end": {"line": 0, "character": 10}
}

# 格式化文档
POST /api/editor/format
```

#### 工作区操作
```http
# 获取工作区信息
GET /api/workspace

# 打开文件夹
POST /api/workspace/open
{
  "path": "/path/to/folder",
  "newWindow": false
}
```

#### 调试操作
```http
# 获取调试会话
GET /api/debug/sessions

# 启动调试
POST /api/debug/start
{
  "configuration": {
    "name": "Debug Config",
    "type": "node",
    "request": "launch",
    "program": "${workspaceFolder}/app.js"
  }
}

# 停止调试
POST /api/debug/stop

# 设置断点
POST /api/debug/breakpoint
{
  "file": "/path/to/file",
  "line": 10,
  "enabled": true,
  "condition": "x > 5"
}

# 调试操作
POST /api/debug/action
{
  "action": "continue" // continue, stepOver, stepInto, stepOut, pause, restart
}
```

#### 终端操作
```http
# 获取终端列表
GET /api/terminal

# 创建终端
POST /api/terminal/create
{
  "name": "My Terminal",
  "cwd": "/path/to/working/directory",
  "env": {"NODE_ENV": "development"}
}

# 发送命令
POST /api/terminal/send
{
  "text": "npm start",
  "addNewLine": true
}
```

## 安全性

### API密钥认证

在设置中配置 `apiKey`，然后在请求头中包含：
```http
X-API-Key: your-api-key-here
```

### CORS配置

配置 `allowedOrigins` 限制允许访问的域名。

### 速率限制

内置速率限制，默认每分钟100个请求。

## 开发

### 项目结构
```
├── src/
│   ├── extension.ts          # 插件入口
│   ├── server.ts            # HTTP服务器
│   ├── types.ts             # 类型定义
│   ├── auth.ts              # 认证管理
│   └── controllers/         # API控制器
│       ├── fileController.ts
│       ├── editorController.ts
│       ├── workspaceController.ts
│       ├── debugController.ts
│       └── terminalController.ts
├── web/                     # Web控制面板
│   ├── index.html
│   └── script.js
├── package.json
└── tsconfig.json
```

### 构建和测试

```bash
# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 监听文件变化
npm run watch

# 打包插件
vsce package
```

## 故障排除

### 服务器无法启动
- 检查端口是否被占用
- 确认防火墙设置
- 查看VSCode开发者控制台的错误信息

### API请求失败
- 验证API密钥是否正确
- 检查CORS设置
- 确认请求格式是否正确

### 权限问题
- 确保VSCode有足够的文件系统权限
- 检查工作区是否正确打开

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License

## 更新日志

### v0.0.1
- 初始版本
- 基础的远程控制功能
- Web控制面板
- API密钥认证
