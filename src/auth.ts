import * as vscode from 'vscode';
import * as crypto from 'crypto';

export class AuthManager {
    private static instance: AuthManager;
    private apiKeys: Set<string> = new Set();
    private sessionTokens: Map<string, { expires: number; permissions: string[] }> = new Map();

    private constructor() {
        this.loadApiKeys();
    }

    public static getInstance(): AuthManager {
        if (!AuthManager.instance) {
            AuthManager.instance = new AuthManager();
        }
        return AuthManager.instance;
    }

    private loadApiKeys() {
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const apiKey = config.get<string>('apiKey');
        
        if (apiKey) {
            this.apiKeys.add(apiKey);
        }
    }

    public validateApiKey(key: string): boolean {
        if (this.apiKeys.size === 0) {
            // 如果没有设置API密钥，则允许所有请求
            return true;
        }
        return this.apiKeys.has(key);
    }

    public generateSessionToken(permissions: string[] = ['read', 'write']): string {
        const token = crypto.randomBytes(32).toString('hex');
        const expires = Date.now() + (24 * 60 * 60 * 1000); // 24小时后过期
        
        this.sessionTokens.set(token, { expires, permissions });
        return token;
    }

    public validateSessionToken(token: string): { valid: boolean; permissions?: string[] } {
        const session = this.sessionTokens.get(token);
        
        if (!session) {
            return { valid: false };
        }

        if (Date.now() > session.expires) {
            this.sessionTokens.delete(token);
            return { valid: false };
        }

        return { valid: true, permissions: session.permissions };
    }

    public revokeSessionToken(token: string): boolean {
        return this.sessionTokens.delete(token);
    }

    public cleanupExpiredTokens() {
        const now = Date.now();
        for (const [token, session] of this.sessionTokens.entries()) {
            if (now > session.expires) {
                this.sessionTokens.delete(token);
            }
        }
    }

    public addApiKey(key: string): void {
        this.apiKeys.add(key);
        this.saveApiKeys();
    }

    public removeApiKey(key: string): boolean {
        const removed = this.apiKeys.delete(key);
        if (removed) {
            this.saveApiKeys();
        }
        return removed;
    }

    public listApiKeys(): string[] {
        return Array.from(this.apiKeys).map(key => 
            key.substring(0, 8) + '...' + key.substring(key.length - 8)
        );
    }

    private saveApiKeys() {
        // 注意：在实际应用中，API密钥应该存储在安全的地方
        // 这里只是示例，实际使用时应该考虑加密存储
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const keys = Array.from(this.apiKeys);
        if (keys.length > 0) {
            config.update('apiKey', keys[0], vscode.ConfigurationTarget.Global);
        }
    }

    public hasPermission(token: string, permission: string): boolean {
        const validation = this.validateSessionToken(token);
        if (!validation.valid || !validation.permissions) {
            return false;
        }
        return validation.permissions.includes(permission) || validation.permissions.includes('admin');
    }

    public generateApiKey(): string {
        return crypto.randomBytes(32).toString('hex');
    }
}

// 权限常量
export const PERMISSIONS = {
    READ: 'read',
    WRITE: 'write',
    DEBUG: 'debug',
    TERMINAL: 'terminal',
    WORKSPACE: 'workspace',
    ADMIN: 'admin'
} as const;

// 中间件函数
export function requirePermission(permission: string) {
    return (req: any, res: any, next: any) => {
        const authManager = AuthManager.getInstance();
        const token = req.headers['x-session-token'];
        
        if (!token) {
            return res.status(401).json({
                success: false,
                error: 'Session token required',
                timestamp: Date.now()
            });
        }

        if (!authManager.hasPermission(token, permission)) {
            return res.status(403).json({
                success: false,
                error: `Permission '${permission}' required`,
                timestamp: Date.now()
            });
        }

        next();
    };
}

// 速率限制
export class RateLimiter {
    private requests: Map<string, number[]> = new Map();
    private readonly maxRequests: number;
    private readonly windowMs: number;

    constructor(maxRequests: number = 100, windowMs: number = 60000) {
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
    }

    public isAllowed(clientId: string): boolean {
        const now = Date.now();
        const requests = this.requests.get(clientId) || [];
        
        // 清理过期的请求记录
        const validRequests = requests.filter(time => now - time < this.windowMs);
        
        if (validRequests.length >= this.maxRequests) {
            return false;
        }

        validRequests.push(now);
        this.requests.set(clientId, validRequests);
        return true;
    }

    public getRemainingRequests(clientId: string): number {
        const requests = this.requests.get(clientId) || [];
        const now = Date.now();
        const validRequests = requests.filter(time => now - time < this.windowMs);
        return Math.max(0, this.maxRequests - validRequests.length);
    }
}
