import * as vscode from 'vscode';
import * as express from 'express';
import * as cors from 'cors';
import * as bodyParser from 'body-parser';
import { Server } from 'http';
import { ApiResponse, ServerStatus } from './types';
import { FileController } from './controllers/fileController';
import { EditorController } from './controllers/editorController';
import { WorkspaceController } from './controllers/workspaceController';
import { DebugController } from './controllers/debugController';
import { TerminalController } from './controllers/terminalController';

export class RemoteControlServer {
    private app: express.Application;
    private server: Server | undefined;
    private context: vscode.ExtensionContext;
    private startTime: number | undefined;
    
    // 控制器实例
    private fileController: FileController;
    private editorController: EditorController;
    private workspaceController: WorkspaceController;
    private debugController: DebugController;
    private terminalController: TerminalController;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.app = express();
        
        // 初始化控制器
        this.fileController = new FileController();
        this.editorController = new EditorController();
        this.workspaceController = new WorkspaceController();
        this.debugController = new DebugController();
        this.terminalController = new TerminalController();
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    private setupMiddleware() {
        // CORS配置
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const allowedOrigins = config.get<string[]>('allowedOrigins') || ['*'];
        
        this.app.use(cors({
            origin: allowedOrigins.includes('*') ? true : allowedOrigins,
            credentials: true
        }));

        // 解析JSON请求体
        this.app.use(bodyParser.json({ limit: '10mb' }));
        this.app.use(bodyParser.urlencoded({ extended: true }));

        // API密钥认证中间件
        this.app.use(this.authMiddleware.bind(this));

        // 错误处理中间件在路由之后添加
    }

    private authMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const apiKey = config.get<string>('apiKey');
        
        // 如果没有设置API密钥，则跳过认证
        if (!apiKey) {
            return next();
        }

        const providedKey = req.headers['x-api-key'] || req.query.apiKey;
        
        if (providedKey !== apiKey) {
            return res.status(401).json(this.createResponse(false, null, 'Invalid API key'));
        }

        next();
    }

    private errorHandler(error: any, req: express.Request, res: express.Response, next: express.NextFunction) {
        console.error('API Error:', error);
        res.status(500).json(this.createResponse(false, null, error.message || 'Internal server error'));
    }

    private setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json(this.createResponse(true, { status: 'ok', uptime: Date.now() - (this.startTime || 0) }));
        });

        // 服务器状态
        this.app.get('/status', (req, res) => {
            res.json(this.createResponse(true, this.getStatus()));
        });

        // 文件操作路由
        this.app.get('/api/files', (req: any, res: any) => this.fileController.listFiles(req, res));
        this.app.get('/api/files/*', (req: any, res: any) => this.fileController.getFile(req, res));
        this.app.post('/api/files/open', (req: any, res: any) => this.fileController.openFile(req, res));
        this.app.post('/api/files/save', (req: any, res: any) => this.fileController.saveFile(req, res));
        this.app.delete('/api/files/*', (req: any, res: any) => this.fileController.deleteFile(req, res));

        // 编辑器操作路由
        this.app.get('/api/editor', (req: any, res: any) => this.editorController.getEditorInfo(req, res));
        this.app.post('/api/editor/edit', (req: any, res: any) => this.editorController.editText(req, res));
        this.app.post('/api/editor/cursor', (req: any, res: any) => this.editorController.moveCursor(req, res));
        this.app.post('/api/editor/select', (req: any, res: any) => this.editorController.selectText(req, res));
        this.app.post('/api/editor/format', (req: any, res: any) => this.editorController.formatDocument(req, res));

        // 工作区操作路由
        this.app.get('/api/workspace', (req: any, res: any) => this.workspaceController.getWorkspaceInfo(req, res));
        this.app.post('/api/workspace/open', (req: any, res: any) => this.workspaceController.openFolder(req, res));

        // 调试操作路由
        this.app.get('/api/debug/sessions', (req: any, res: any) => this.debugController.getDebugSessions(req, res));
        this.app.post('/api/debug/start', (req: any, res: any) => this.debugController.startDebugging(req, res));
        this.app.post('/api/debug/stop', (req: any, res: any) => this.debugController.stopDebugging(req, res));
        this.app.post('/api/debug/breakpoint', (req: any, res: any) => this.debugController.toggleBreakpoint(req, res));

        // 终端操作路由
        this.app.get('/api/terminal', (req: any, res: any) => this.terminalController.getTerminals(req, res));
        this.app.post('/api/terminal/create', (req: any, res: any) => this.terminalController.createTerminal(req, res));
        this.app.post('/api/terminal/send', (req: any, res: any) => this.terminalController.sendText(req, res));

        // 静态文件服务 - Web控制面板
        this.app.use('/web', express.static(require('path').join(__dirname, '../web')));

        // 错误处理中间件
        this.app.use(this.errorHandler.bind(this));
    }

    public start(): void {
        if (this.server) {
            vscode.window.showWarningMessage('Remote Control Server is already running');
            return;
        }

        const config = vscode.workspace.getConfiguration('vscode-remote-control');
        const port = config.get<number>('port') || 8080;

        this.server = this.app.listen(port, () => {
            this.startTime = Date.now();
            vscode.window.showInformationMessage(`Remote Control Server started on port ${port}`);
            console.log(`VSCode Remote Control Server listening on port ${port}`);
        });

        this.server?.on('error', (error: any) => {
            vscode.window.showErrorMessage(`Failed to start server: ${error.message}`);
            this.server = undefined;
        });
    }

    public stop(): void {
        if (this.server) {
            this.server.close(() => {
                vscode.window.showInformationMessage('Remote Control Server stopped');
                console.log('VSCode Remote Control Server stopped');
            });
            this.server = undefined;
            this.startTime = undefined;
        } else {
            vscode.window.showWarningMessage('Remote Control Server is not running');
        }
    }

    public getStatus(): ServerStatus {
        return {
            running: !!this.server,
            port: this.server ? (this.server.address() as any)?.port : undefined,
            startTime: this.startTime
        };
    }

    private createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
