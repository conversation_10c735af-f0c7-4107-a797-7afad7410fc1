// API 请求和响应类型定义

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: number;
}

export interface ServerStatus {
    running: boolean;
    port?: number;
    startTime?: number;
}

// 文件操作相关类型
export interface FileInfo {
    path: string;
    name: string;
    isDirectory: boolean;
    size?: number;
    lastModified?: number;
}

export interface OpenFileRequest {
    path: string;
    viewColumn?: number;
    preserveFocus?: boolean;
}

export interface SaveFileRequest {
    path?: string; // 如果为空则保存当前活动文件
    content?: string; // 如果为空则保存当前内容
}

// 编辑器操作相关类型
export interface EditorInfo {
    document: {
        fileName: string;
        languageId: string;
        lineCount: number;
        isDirty: boolean;
        isUntitled: boolean;
    };
    selection: {
        start: { line: number; character: number };
        end: { line: number; character: number };
        text: string;
    };
    visibleRange: {
        start: { line: number; character: number };
        end: { line: number; character: number };
    };
}

export interface TextEditRequest {
    range?: {
        start: { line: number; character: number };
        end: { line: number; character: number };
    };
    text: string;
}

export interface CursorMoveRequest {
    line: number;
    character: number;
    select?: boolean; // 是否选择文本
}

export interface TextSelectionRequest {
    start: { line: number; character: number };
    end: { line: number; character: number };
}

// 工作区操作相关类型
export interface WorkspaceInfo {
    folders: Array<{
        name: string;
        uri: string;
    }>;
    name?: string;
}

export interface OpenFolderRequest {
    path: string;
    newWindow?: boolean;
}

// 调试操作相关类型
export interface DebugSession {
    id: string;
    name: string;
    type: string;
    configuration: any;
}

export interface StartDebugRequest {
    configuration?: any;
    folder?: string;
}

export interface BreakpointRequest {
    file: string;
    line: number;
    enabled?: boolean;
    condition?: string;
}

// 终端操作相关类型
export interface TerminalInfo {
    name: string;
    processId?: number;
    creationOptions: any;
}

export interface CreateTerminalRequest {
    name?: string;
    cwd?: string;
    env?: { [key: string]: string };
}

export interface SendTerminalTextRequest {
    text: string;
    addNewLine?: boolean;
}
