import * as vscode from 'vscode';
import { RemoteControlServer } from './server';

let server: RemoteControlServer | undefined;

export function activate(context: vscode.ExtensionContext) {
    console.log('VSCode Remote Control extension is now active!');

    // 创建服务器实例
    server = new RemoteControlServer(context);

    // 注册命令
    const startCommand = vscode.commands.registerCommand('vscode-remote-control.start', () => {
        server?.start();
    });

    const stopCommand = vscode.commands.registerCommand('vscode-remote-control.stop', () => {
        server?.stop();
    });

    const statusCommand = vscode.commands.registerCommand('vscode-remote-control.status', () => {
        const status = server?.getStatus();
        vscode.window.showInformationMessage(`Remote Control Server: ${status?.running ? 'Running on port ' + status.port : 'Stopped'}`);
    });

    context.subscriptions.push(startCommand, stopCommand, statusCommand);

    // 如果配置了自动启动，则启动服务器
    const config = vscode.workspace.getConfiguration('vscode-remote-control');
    if (config.get('autoStart')) {
        server.start();
    }
}

export function deactivate() {
    server?.stop();
}
