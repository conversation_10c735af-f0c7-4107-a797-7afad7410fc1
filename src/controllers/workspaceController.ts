import * as vscode from 'vscode';
import * as express from 'express';
import { ApiResponse, WorkspaceInfo, OpenFolderRequest } from '../types';

export class WorkspaceController {
    
    public async getWorkspaceInfo(req: express.Request, res: express.Response) {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const workspaceName = vscode.workspace.name;

            const workspaceInfo: WorkspaceInfo = {
                folders: workspaceFolders ? workspaceFolders.map(folder => ({
                    name: folder.name,
                    uri: folder.uri.toString()
                })) : [],
                name: workspaceName
            };

            res.json(this.createResponse(true, workspaceInfo));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async openFolder(req: express.Request, res: express.Response) {
        try {
            const { path, newWindow }: OpenFolderRequest = req.body;
            
            if (!path) {
                return res.status(400).json(this.createResponse(false, null, 'Folder path is required'));
            }

            const uri = vscode.Uri.file(path);
            
            await vscode.commands.executeCommand('vscode.openFolder', uri, {
                forceNewWindow: newWindow || false
            });

            res.json(this.createResponse(true, {
                path,
                opened: true,
                newWindow: newWindow || false
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async closeWorkspace(req: express.Request, res: express.Response) {
        try {
            await vscode.commands.executeCommand('workbench.action.closeFolder');
            
            res.json(this.createResponse(true, { closed: true }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async getOpenEditors(req: express.Request, res: express.Response) {
        try {
            const tabGroups = vscode.window.tabGroups;
            const openEditors: any[] = [];

            tabGroups.all.forEach((group, groupIndex) => {
                group.tabs.forEach((tab, tabIndex) => {
                    if (tab.input instanceof vscode.TabInputText) {
                        openEditors.push({
                            groupIndex,
                            tabIndex,
                            fileName: tab.input.uri.fsPath,
                            label: tab.label,
                            isDirty: tab.isDirty,
                            isActive: tab.isActive,
                            isPinned: tab.isPinned
                        });
                    }
                });
            });

            res.json(this.createResponse(true, openEditors));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async closeEditor(req: express.Request, res: express.Response) {
        try {
            const { fileName, groupIndex, tabIndex } = req.body;
            
            if (fileName) {
                // 根据文件名关闭编辑器
                const tabGroups = vscode.window.tabGroups;
                for (const group of tabGroups.all) {
                    for (const tab of group.tabs) {
                        if (tab.input instanceof vscode.TabInputText && 
                            tab.input.uri.fsPath === fileName) {
                            await vscode.window.tabGroups.close(tab);
                            return res.json(this.createResponse(true, { closed: true, fileName }));
                        }
                    }
                }
                return res.status(404).json(this.createResponse(false, null, 'Editor not found'));
            } else if (groupIndex !== undefined && tabIndex !== undefined) {
                // 根据组和标签索引关闭编辑器
                const group = vscode.window.tabGroups.all[groupIndex];
                if (group && group.tabs[tabIndex]) {
                    await vscode.window.tabGroups.close(group.tabs[tabIndex]);
                    return res.json(this.createResponse(true, { closed: true, groupIndex, tabIndex }));
                }
                return res.status(404).json(this.createResponse(false, null, 'Editor not found'));
            } else {
                // 关闭当前活动编辑器
                await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                return res.json(this.createResponse(true, { closed: true }));
            }
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async switchToEditor(req: express.Request, res: express.Response) {
        try {
            const { fileName, groupIndex, tabIndex } = req.body;
            
            if (fileName) {
                // 根据文件名切换到编辑器
                const uri = vscode.Uri.file(fileName);
                await vscode.window.showTextDocument(uri);
                return res.json(this.createResponse(true, { switched: true, fileName }));
            } else if (groupIndex !== undefined && tabIndex !== undefined) {
                // 根据组和标签索引切换到编辑器
                const group = vscode.window.tabGroups.all[groupIndex];
                if (group && group.tabs[tabIndex]) {
                    const tab = group.tabs[tabIndex];
                    if (tab.input instanceof vscode.TabInputText) {
                        await vscode.window.showTextDocument(tab.input.uri);
                        return res.json(this.createResponse(true, { switched: true, groupIndex, tabIndex }));
                    }
                }
                return res.status(404).json(this.createResponse(false, null, 'Editor not found'));
            } else {
                return res.status(400).json(this.createResponse(false, null, 'fileName or groupIndex/tabIndex is required'));
            }
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    private createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
