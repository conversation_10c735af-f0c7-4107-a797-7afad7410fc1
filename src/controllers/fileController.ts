import * as vscode from 'vscode';
import * as express from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { ApiResponse, FileInfo, OpenFileRequest, SaveFileRequest } from '../types';

export class FileController {
    
    public async listFiles(req: express.Request, res: express.Response) {
        try {
            const dirPath = req.query.path as string || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
            
            if (!dirPath) {
                return res.status(400).json(this.createResponse(false, null, 'No workspace folder found'));
            }

            const files = await this.getDirectoryContents(dirPath);
            res.json(this.createResponse(true, files));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async getFile(req: express.Request, res: express.Response) {
        try {
            const filePath = req.params[0]; // 获取通配符匹配的路径
            
            if (!filePath) {
                return res.status(400).json(this.createResponse(false, null, 'File path is required'));
            }

            const fullPath = path.isAbsolute(filePath) ? filePath : 
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);

            const content = await fs.promises.readFile(fullPath, 'utf8');
            const stats = await fs.promises.stat(fullPath);
            
            const fileInfo: FileInfo & { content: string } = {
                path: fullPath,
                name: path.basename(fullPath),
                isDirectory: stats.isDirectory(),
                size: stats.size,
                lastModified: stats.mtime.getTime(),
                content
            };

            res.json(this.createResponse(true, fileInfo));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async openFile(req: express.Request, res: express.Response) {
        try {
            const { path: filePath, viewColumn, preserveFocus }: OpenFileRequest = req.body;
            
            if (!filePath) {
                return res.status(400).json(this.createResponse(false, null, 'File path is required'));
            }

            const fullPath = path.isAbsolute(filePath) ? filePath : 
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);

            const uri = vscode.Uri.file(fullPath);
            const document = await vscode.workspace.openTextDocument(uri);
            
            const editor = await vscode.window.showTextDocument(document, {
                viewColumn: viewColumn as vscode.ViewColumn,
                preserveFocus: preserveFocus || false
            });

            res.json(this.createResponse(true, {
                path: document.fileName,
                lineCount: document.lineCount,
                languageId: document.languageId
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async saveFile(req: express.Request, res: express.Response) {
        try {
            const { path: filePath, content }: SaveFileRequest = req.body;
            
            if (filePath && content !== undefined) {
                // 保存指定路径的文件
                const fullPath = path.isAbsolute(filePath) ? filePath : 
                    path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);
                
                await fs.promises.writeFile(fullPath, content, 'utf8');
                res.json(this.createResponse(true, { path: fullPath, saved: true }));
            } else {
                // 保存当前活动文件
                const activeEditor = vscode.window.activeTextEditor;
                if (!activeEditor) {
                    return res.status(400).json(this.createResponse(false, null, 'No active editor'));
                }

                if (content !== undefined) {
                    // 替换当前文档内容
                    const document = activeEditor.document;
                    const fullRange = new vscode.Range(
                        document.positionAt(0),
                        document.positionAt(document.getText().length)
                    );
                    
                    await activeEditor.edit(editBuilder => {
                        editBuilder.replace(fullRange, content);
                    });
                }

                await activeEditor.document.save();
                res.json(this.createResponse(true, { 
                    path: activeEditor.document.fileName, 
                    saved: true 
                }));
            }
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async deleteFile(req: express.Request, res: express.Response) {
        try {
            const filePath = req.params[0];
            
            if (!filePath) {
                return res.status(400).json(this.createResponse(false, null, 'File path is required'));
            }

            const fullPath = path.isAbsolute(filePath) ? filePath : 
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath);

            await fs.promises.unlink(fullPath);
            res.json(this.createResponse(true, { path: fullPath, deleted: true }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    private async getDirectoryContents(dirPath: string): Promise<FileInfo[]> {
        const items = await fs.promises.readdir(dirPath);
        const files: FileInfo[] = [];

        for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stats = await fs.promises.stat(itemPath);
            
            files.push({
                path: itemPath,
                name: item,
                isDirectory: stats.isDirectory(),
                size: stats.isDirectory() ? undefined : stats.size,
                lastModified: stats.mtime.getTime()
            });
        }

        return files.sort((a, b) => {
            // 目录排在前面
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });
    }

    private createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
