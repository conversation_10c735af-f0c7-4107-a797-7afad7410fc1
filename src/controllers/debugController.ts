import * as vscode from 'vscode';
import * as express from 'express';
import { ApiResponse, DebugSession, StartDebugRequest, BreakpointRequest } from '../types';

export class DebugController {
    
    public async getDebugSessions(req: express.Request, res: express.Response) {
        try {
            const sessions = vscode.debug.activeDebugSession ? [vscode.debug.activeDebugSession] : [];
            
            const sessionInfo: DebugSession[] = sessions.map(session => ({
                id: session.id,
                name: session.name,
                type: session.type,
                configuration: session.configuration
            }));

            res.json(this.createResponse(true, {
                sessions: sessionInfo,
                activeSession: vscode.debug.activeDebugSession?.id || null
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async startDebugging(req: express.Request, res: express.Response) {
        try {
            const { configuration, folder }: StartDebugRequest = req.body;
            
            let workspaceFolder: vscode.WorkspaceFolder | undefined;
            
            if (folder) {
                workspaceFolder = vscode.workspace.workspaceFolders?.find(f => f.uri.fsPath === folder);
            } else {
                workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            }

            const success = await vscode.debug.startDebugging(workspaceFolder, configuration);
            
            if (success) {
                res.json(this.createResponse(true, {
                    started: true,
                    sessionId: vscode.debug.activeDebugSession?.id
                }));
            } else {
                res.status(500).json(this.createResponse(false, null, 'Failed to start debugging'));
            }
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async stopDebugging(req: express.Request, res: express.Response) {
        try {
            const { sessionId } = req.body;
            
            if (sessionId) {
                // 停止指定的调试会话
                const session = vscode.debug.activeDebugSession;
                if (session && session.id === sessionId) {
                    await vscode.debug.stopDebugging(session);
                } else {
                    return res.status(404).json(this.createResponse(false, null, 'Debug session not found'));
                }
            } else {
                // 停止当前活动的调试会话
                if (vscode.debug.activeDebugSession) {
                    await vscode.debug.stopDebugging();
                } else {
                    return res.status(404).json(this.createResponse(false, null, 'No active debug session'));
                }
            }

            res.json(this.createResponse(true, { stopped: true }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async toggleBreakpoint(req: express.Request, res: express.Response) {
        try {
            const { file, line, enabled, condition }: BreakpointRequest = req.body;
            
            if (!file || line === undefined) {
                return res.status(400).json(this.createResponse(false, null, 'File and line are required'));
            }

            const uri = vscode.Uri.file(file);
            const position = new vscode.Position(line, 0);
            
            // 获取现有断点
            const existingBreakpoints = vscode.debug.breakpoints.filter(bp => 
                bp instanceof vscode.SourceBreakpoint && 
                bp.location.uri.fsPath === uri.fsPath &&
                bp.location.range.start.line === line
            ) as vscode.SourceBreakpoint[];

            if (existingBreakpoints.length > 0) {
                // 移除现有断点
                vscode.debug.removeBreakpoints(existingBreakpoints);
                res.json(this.createResponse(true, { 
                    file, 
                    line, 
                    action: 'removed' 
                }));
            } else {
                // 添加新断点
                const location = new vscode.Location(uri, position);
                const breakpoint = new vscode.SourceBreakpoint(location, enabled !== false, condition);
                vscode.debug.addBreakpoints([breakpoint]);
                
                res.json(this.createResponse(true, { 
                    file, 
                    line, 
                    action: 'added',
                    enabled: enabled !== false,
                    condition 
                }));
            }
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async getBreakpoints(req: express.Request, res: express.Response) {
        try {
            const breakpoints = vscode.debug.breakpoints
                .filter(bp => bp instanceof vscode.SourceBreakpoint)
                .map(bp => {
                    const sourceBp = bp as vscode.SourceBreakpoint;
                    return {
                        file: sourceBp.location.uri.fsPath,
                        line: sourceBp.location.range.start.line,
                        enabled: sourceBp.enabled,
                        condition: sourceBp.condition
                    };
                });

            res.json(this.createResponse(true, breakpoints));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async debugAction(req: express.Request, res: express.Response) {
        try {
            const { action } = req.body;
            
            if (!vscode.debug.activeDebugSession) {
                return res.status(404).json(this.createResponse(false, null, 'No active debug session'));
            }

            let command: string;
            switch (action) {
                case 'continue':
                    command = 'workbench.action.debug.continue';
                    break;
                case 'stepOver':
                    command = 'workbench.action.debug.stepOver';
                    break;
                case 'stepInto':
                    command = 'workbench.action.debug.stepInto';
                    break;
                case 'stepOut':
                    command = 'workbench.action.debug.stepOut';
                    break;
                case 'pause':
                    command = 'workbench.action.debug.pause';
                    break;
                case 'restart':
                    command = 'workbench.action.debug.restart';
                    break;
                default:
                    return res.status(400).json(this.createResponse(false, null, 'Invalid debug action'));
            }

            await vscode.commands.executeCommand(command);
            res.json(this.createResponse(true, { action, executed: true }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    private createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
