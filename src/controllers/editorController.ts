import * as vscode from 'vscode';
import * as express from 'express';
import { ApiResponse, EditorInfo, TextEditRequest, CursorMoveRequest, TextSelectionRequest } from '../types';

export class EditorController {
    
    public async getEditorInfo(req: express.Request, res: express.Response) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }

            const document = activeEditor.document;
            const selection = activeEditor.selection;
            const visibleRanges = activeEditor.visibleRanges;

            const editorInfo: EditorInfo = {
                document: {
                    fileName: document.fileName,
                    languageId: document.languageId,
                    lineCount: document.lineCount,
                    isDirty: document.isDirty,
                    isUntitled: document.isUntitled
                },
                selection: {
                    start: {
                        line: selection.start.line,
                        character: selection.start.character
                    },
                    end: {
                        line: selection.end.line,
                        character: selection.end.character
                    },
                    text: document.getText(selection)
                },
                visibleRange: {
                    start: {
                        line: visibleRanges[0]?.start.line || 0,
                        character: visibleRanges[0]?.start.character || 0
                    },
                    end: {
                        line: visibleRanges[0]?.end.line || 0,
                        character: visibleRanges[0]?.end.character || 0
                    }
                }
            };

            res.json(this.createResponse(true, editorInfo));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async editText(req: express.Request, res: express.Response) {
        try {
            const { range, text }: TextEditRequest = req.body;
            const activeEditor = vscode.window.activeTextEditor;
            
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }

            const success = await activeEditor.edit(editBuilder => {
                if (range) {
                    const vscodeRange = new vscode.Range(
                        new vscode.Position(range.start.line, range.start.character),
                        new vscode.Position(range.end.line, range.end.character)
                    );
                    editBuilder.replace(vscodeRange, text);
                } else {
                    // 如果没有指定范围，在当前光标位置插入文本
                    editBuilder.insert(activeEditor.selection.active, text);
                }
            });

            if (success) {
                res.json(this.createResponse(true, { edited: true }));
            } else {
                res.status(500).json(this.createResponse(false, null, 'Failed to edit text'));
            }
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async moveCursor(req: express.Request, res: express.Response) {
        try {
            const { line, character, select }: CursorMoveRequest = req.body;
            const activeEditor = vscode.window.activeTextEditor;
            
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }

            const position = new vscode.Position(line, character);
            
            if (select) {
                // 扩展选择到新位置
                const newSelection = new vscode.Selection(activeEditor.selection.anchor, position);
                activeEditor.selection = newSelection;
            } else {
                // 移动光标到新位置
                const newSelection = new vscode.Selection(position, position);
                activeEditor.selection = newSelection;
            }

            // 滚动到光标位置
            activeEditor.revealRange(new vscode.Range(position, position));

            res.json(this.createResponse(true, {
                position: { line, character },
                selected: select || false
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async selectText(req: express.Request, res: express.Response) {
        try {
            const { start, end }: TextSelectionRequest = req.body;
            const activeEditor = vscode.window.activeTextEditor;
            
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }

            const startPosition = new vscode.Position(start.line, start.character);
            const endPosition = new vscode.Position(end.line, end.character);
            const selection = new vscode.Selection(startPosition, endPosition);
            
            activeEditor.selection = selection;
            activeEditor.revealRange(selection);

            const selectedText = activeEditor.document.getText(selection);

            res.json(this.createResponse(true, {
                selection: { start, end },
                text: selectedText
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async formatDocument(req: express.Request, res: express.Response) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }

            await vscode.commands.executeCommand('editor.action.formatDocument');

            res.json(this.createResponse(true, { formatted: true }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async getDocumentText(req: express.Request, res: express.Response) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            
            if (!activeEditor) {
                return res.status(404).json(this.createResponse(false, null, 'No active editor'));
            }

            const document = activeEditor.document;
            const text = document.getText();

            res.json(this.createResponse(true, {
                text,
                lineCount: document.lineCount,
                fileName: document.fileName
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    private createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
