import * as vscode from 'vscode';
import * as express from 'express';
import { ApiResponse, TerminalInfo, CreateTerminalRequest, SendTerminalTextRequest } from '../types';

export class TerminalController {
    private terminals: Map<string, vscode.Terminal> = new Map();
    
    public async getTerminals(req: express.Request, res: express.Response) {
        try {
            const terminals = vscode.window.terminals;

            const terminalInfo: TerminalInfo[] = await Promise.all(terminals.map(async terminal => ({
                name: terminal.name,
                processId: await terminal.processId,
                creationOptions: terminal.creationOptions
            })));

            res.json(this.createResponse(true, terminalInfo));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async createTerminal(req: express.Request, res: express.Response) {
        try {
            const { name, cwd, env }: CreateTerminalRequest = req.body;
            
            const options: vscode.TerminalOptions = {
                name: name || 'Remote Terminal',
                cwd: cwd,
                env: env
            };

            const terminal = vscode.window.createTerminal(options);
            
            // 存储终端引用
            this.terminals.set(terminal.name, terminal);
            
            // 显示终端
            terminal.show();

            res.json(this.createResponse(true, {
                name: terminal.name,
                processId: await terminal.processId,
                created: true
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async sendText(req: express.Request, res: express.Response) {
        try {
            const { text, addNewLine }: SendTerminalTextRequest = req.body;
            const { terminalName } = req.query;
            
            if (!text) {
                return res.status(400).json(this.createResponse(false, null, 'Text is required'));
            }

            let terminal: vscode.Terminal | undefined;
            
            if (terminalName) {
                // 发送到指定终端
                terminal = vscode.window.terminals.find(t => t.name === terminalName);
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
                }
            } else {
                // 发送到活动终端
                terminal = vscode.window.activeTerminal;
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'No active terminal'));
                }
            }

            terminal.sendText(text, addNewLine !== false);
            
            res.json(this.createResponse(true, {
                terminalName: terminal.name,
                textSent: text,
                addNewLine: addNewLine !== false
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async showTerminal(req: express.Request, res: express.Response) {
        try {
            const { terminalName } = req.body;
            
            let terminal: vscode.Terminal | undefined;
            
            if (terminalName) {
                terminal = vscode.window.terminals.find(t => t.name === terminalName);
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
                }
            } else {
                terminal = vscode.window.activeTerminal;
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'No active terminal'));
                }
            }

            terminal.show();
            
            res.json(this.createResponse(true, {
                terminalName: terminal.name,
                shown: true
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async hideTerminal(req: express.Request, res: express.Response) {
        try {
            const { terminalName } = req.body;
            
            let terminal: vscode.Terminal | undefined;
            
            if (terminalName) {
                terminal = vscode.window.terminals.find(t => t.name === terminalName);
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
                }
            } else {
                terminal = vscode.window.activeTerminal;
                if (!terminal) {
                    return res.status(404).json(this.createResponse(false, null, 'No active terminal'));
                }
            }

            terminal.hide();
            
            res.json(this.createResponse(true, {
                terminalName: terminal.name,
                hidden: true
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    public async disposeTerminal(req: express.Request, res: express.Response) {
        try {
            const { terminalName } = req.body;
            
            if (!terminalName) {
                return res.status(400).json(this.createResponse(false, null, 'Terminal name is required'));
            }

            const terminal = vscode.window.terminals.find(t => t.name === terminalName);
            if (!terminal) {
                return res.status(404).json(this.createResponse(false, null, 'Terminal not found'));
            }

            terminal.dispose();
            this.terminals.delete(terminalName);
            
            res.json(this.createResponse(true, {
                terminalName,
                disposed: true
            }));
        } catch (error: any) {
            res.status(500).json(this.createResponse(false, null, error.message));
        }
    }

    private createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
        return {
            success,
            data,
            error,
            timestamp: Date.now()
        };
    }
}
