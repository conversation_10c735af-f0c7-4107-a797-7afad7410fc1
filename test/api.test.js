// 简单的API测试脚本
// 运行前请确保VSCode插件已启动并且服务器正在运行

const baseUrl = 'http://localhost:8080';
const apiKey = ''; // 如果设置了API密钥，请在这里填入

async function makeRequest(endpoint, method = 'GET', data = null) {
    const url = `${baseUrl}${endpoint}`;
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        }
    };

    if (apiKey) {
        options.headers['X-API-Key'] = apiKey;
    }

    if (data) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(url, options);
        const result = await response.json();
        return { status: response.status, data: result };
    } catch (error) {
        return { error: error.message };
    }
}

async function runTests() {
    console.log('🚀 Starting VSCode Remote Control API Tests\n');

    // 测试健康检查
    console.log('1. Testing health check...');
    const health = await makeRequest('/health');
    if (health.data && health.data.success) {
        console.log('✅ Health check passed');
    } else {
        console.log('❌ Health check failed:', health);
        return;
    }

    // 测试服务器状态
    console.log('\n2. Testing server status...');
    const status = await makeRequest('/status');
    if (status.data && status.data.success) {
        console.log('✅ Server status retrieved:', status.data.data);
    } else {
        console.log('❌ Server status failed:', status);
    }

    // 测试工作区信息
    console.log('\n3. Testing workspace info...');
    const workspace = await makeRequest('/api/workspace');
    if (workspace.data && workspace.data.success) {
        console.log('✅ Workspace info retrieved');
        console.log('   Folders:', workspace.data.data.folders.length);
    } else {
        console.log('❌ Workspace info failed:', workspace);
    }

    // 测试编辑器信息
    console.log('\n4. Testing editor info...');
    const editor = await makeRequest('/api/editor');
    if (editor.data && editor.data.success) {
        console.log('✅ Editor info retrieved');
        console.log('   File:', editor.data.data.document.fileName);
    } else {
        console.log('⚠️ Editor info failed (no active editor?):', editor);
    }

    // 测试文件列表
    console.log('\n5. Testing file listing...');
    const files = await makeRequest('/api/files');
    if (files.data && files.data.success) {
        console.log('✅ File listing retrieved');
        console.log('   Files count:', files.data.data.length);
    } else {
        console.log('❌ File listing failed:', files);
    }

    // 测试调试会话
    console.log('\n6. Testing debug sessions...');
    const debug = await makeRequest('/api/debug/sessions');
    if (debug.data && debug.data.success) {
        console.log('✅ Debug sessions retrieved');
        console.log('   Active sessions:', debug.data.data.sessions.length);
    } else {
        console.log('❌ Debug sessions failed:', debug);
    }

    // 测试终端列表
    console.log('\n7. Testing terminal list...');
    const terminals = await makeRequest('/api/terminal');
    if (terminals.data && terminals.data.success) {
        console.log('✅ Terminal list retrieved');
        console.log('   Terminals count:', terminals.data.data.length);
    } else {
        console.log('❌ Terminal list failed:', terminals);
    }

    // 测试文本编辑（如果有活动编辑器）
    if (editor.data && editor.data.success) {
        console.log('\n8. Testing text insertion...');
        const edit = await makeRequest('/api/editor/edit', 'POST', {
            text: '\n// Remote control test comment\n'
        });
        if (edit.data && edit.data.success) {
            console.log('✅ Text insertion successful');
        } else {
            console.log('❌ Text insertion failed:', edit);
        }
    }

    console.log('\n🎉 API tests completed!');
}

// 运行测试
if (typeof window === 'undefined') {
    // Node.js环境
    const fetch = require('node-fetch');
    runTests().catch(console.error);
} else {
    // 浏览器环境
    runTests().catch(console.error);
}

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runTests, makeRequest };
}
